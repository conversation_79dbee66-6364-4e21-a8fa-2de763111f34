"""
Enhanced repository analysis task with AI-powered business logic understanding
"""
from celery import current_task
from sqlalchemy.orm import Session
from datetime import datetime
import logging
import json
import asyncio
from typing import Dict, Any, Optional

from .celery_app import celery_app
from ..database import SessionLocal
from ..models import RepoAnalysis
from ..services.intelligent_analysis_service import IntelligentAnalysisService
from ..config import settings

logger = logging.getLogger(__name__)


@celery_app.task(bind=True, max_retries=2)
def analyze_repository_enhanced(self, analysis_id: int, github_token: str):
    """
    Enhanced repository analysis task with AI-powered business logic understanding
    """
    db = SessionLocal()
    try:
        # Get analysis record
        analysis = db.query(RepoAnalysis).filter(RepoAnalysis.id == analysis_id).first()
        if not analysis:
            raise ValueError(f"Analysis {analysis_id} not found")
        
        # Check if any AI API key is available
        if not settings.anthropic_api_key and not settings.openai_api_key:
            logger.warning("No AI API key configured, falling back to basic analysis")
            # Import and run basic analysis as fallback
            from .real_analysis import analyze_repository_real
            return analyze_repository_real(analysis_id, github_token)
        
        # Update status to analyzing
        analysis.status = "analyzing" 
        db.commit()
        
        # Step 1: Initialize (5%)
        self.update_state(
            state='PROGRESS',
            meta={'current': 5, 'total': 100, 'status': 'Initializing AI-powered analysis...'}
        )
        
        intelligent_service = IntelligentAnalysisService()
        
        # Step 2: Gather repository metadata (15%)
        self.update_state(
            state='PROGRESS',
            meta={'current': 15, 'total': 100, 'status': 'Fetching repository metadata...'}
        )
        
        # Step 3: Analyze repository structure (25%)
        self.update_state(
            state='PROGRESS',
            meta={'current': 25, 'total': 100, 'status': 'Analyzing repository structure...'}
        )
        
        # Step 4: Reading key files (40%)
        self.update_state(
            state='PROGRESS',
            meta={'current': 40, 'total': 100, 'status': 'Reading and analyzing key files...'}
        )
        
        # Step 5: AI business logic analysis (60%)
        self.update_state(
            state='PROGRESS', 
            meta={'current': 60, 'total': 100, 'status': 'Performing AI-powered business logic analysis...'}
        )
        
        try:
            comprehensive_analysis = asyncio.run(intelligent_service.analyze_repository_comprehensively(
                analysis.repo_owner,
                analysis.repo_name,
                github_token
            ))
        except Exception as ai_error:
            logger.error(f"AI analysis failed: {str(ai_error)}, falling back to basic analysis")
            # Fallback to basic analysis - implement directly to avoid context issues
            self.update_state(
                state='PROGRESS',
                meta={'current': 50, 'total': 100, 'status': 'Falling back to basic analysis...'}
            )
            
            # Try to gather at least repository tree data for the fallback
            try:
                basic_repo_content = asyncio.run(intelligent_service._gather_repository_content(
                    analysis.repo_owner,
                    analysis.repo_name,
                    github_token
                ))
                repository_tree = basic_repo_content.get("repository_tree", [])
                repository_info = basic_repo_content.get("repository_info", {})
            except Exception as content_error:
                logger.warning(f"Failed to gather basic repository content: {str(content_error)}")
                repository_tree = []
                repository_info = {}
            
            # Use basic scoring instead of AI analysis
            basic_results = {
                "analysis_type": "basic_fallback",
                "fallback_reason": str(ai_error),
                "basic_analysis": True,
                "repository_tree": repository_tree,
                "repository_info": repository_info
            }
            
            # Calculate a basic MCP score (simplified)
            basic_mcp_score = 30.0  # Default fallback score
            
            # Update analysis record with basic results
            analysis.mcp_feasibility_score = basic_mcp_score
            analysis.analysis_results = basic_results
            analysis.status = "completed"
            analysis.completed_at = datetime.utcnow()
            db.commit()
            
            # Complete (100%)
            self.update_state(
                state='SUCCESS',
                meta={
                    'current': 100, 
                    'total': 100, 
                    'status': 'Basic analysis completed (AI analysis failed)'
                }
            )
            
            logger.info(f"Basic fallback analysis {analysis_id} completed with MCP score: {basic_mcp_score}")
            
            return {
                "analysis_id": analysis_id,
                "status": "completed",
                "analysis_type": "basic_fallback",
                "mcp_score": basic_mcp_score,
                "tools_suggested": 0,
                "confidence_score": 0.3,
                "ready_for_generation": False,
                "fallback_reason": str(ai_error)
            }
        
        # Step 6: Generating MCP tool suggestions (75%)
        self.update_state(
            state='PROGRESS',
            meta={'current': 75, 'total': 100, 'status': 'Generating MCP tool suggestions...'}
        )
        
        # Step 7: Calculate enhanced MCP feasibility score (85%)
        self.update_state(
            state='PROGRESS',
            meta={'current': 85, 'total': 100, 'status': 'Calculating enhanced MCP feasibility score...'}
        )
        
        enhanced_mcp_score = calculate_enhanced_mcp_score(comprehensive_analysis)
        
        # Step 8: Preparing recommendations (92%)
        self.update_state(
            state='PROGRESS',
            meta={'current': 92, 'total': 100, 'status': 'Preparing implementation recommendations...'}
        )
        
        # Step 9: Finalize results (98%)
        self.update_state(
            state='PROGRESS',
            meta={'current': 98, 'total': 100, 'status': 'Finalizing enhanced analysis results...'}
        )
        
        # Transform AI analysis data for frontend compatibility
        def transform_ai_data_for_frontend(ai_data):
            """Transform AI analysis data to frontend-expected format"""
            comprehensive = ai_data.get("comprehensive_analysis", {})
            mcp_suggestions = ai_data.get("mcp_suggestions", {})
            
            # Create recommendations from MCP suggestions
            recommendations = []
            categories = mcp_suggestions.get("categories", {})
            
            for category, tools in categories.items():
                if tools:
                    for tool in tools:
                        rec = {
                            "title": tool.get("name", "Unknown Tool"),
                            "description": tool.get("description", "No description available"),
                            "type": "implementation",
                            "priority": "high" if tool.get("business_value") else "medium",
                            "confidence": "high",
                            "reasoning": [
                                f"Business value: {tool.get('business_value', 'Not specified')}",
                                f"Implementation effort: {tool.get('implementation_effort', 'unknown')}",
                                f"Category: {category.replace('_', ' ').title()}"
                            ],
                            "tools": [tool],
                            "use_cases": tool.get("use_cases", []),
                            "effort": tool.get("implementation_effort", "medium"),
                            "timeline": "Post-implementation"
                        }
                        recommendations.append(rec)
            
            # Get repository tree from the analysis data
            repository_tree = ai_data.get("repository_tree", [])
            
            # Create code structure summary
            business_logic = comprehensive.get("business_logic", {})
            api_capabilities = comprehensive.get("api_capabilities", {})
            integration_opportunities = comprehensive.get("integration_opportunities", {})
            
            # Safely check for database indicators
            has_database = False
            try:
                has_database = (
                    "database" in str(integration_opportunities).lower() or 
                    "database" in str(api_capabilities).lower() or
                    "sql" in str(api_capabilities).lower() or
                    "postgres" in str(api_capabilities).lower() or
                    "mysql" in str(api_capabilities).lower()
                )
            except Exception:
                has_database = False
            
            code_structure = {
                "languages": {"Primary": business_logic.get("primary_domain", "Unknown")},
                "has_cli": bool(api_capabilities.get("existing_apis")),
                "has_api": bool(api_capabilities.get("existing_apis")),
                "has_database": has_database,
                "file_count": ai_data.get("analysis_metadata", {}).get("files_analyzed", 0)
            }
            
            return {
                "recommendations": recommendations,
                "repository_tree": repository_tree,
                "code_structure": code_structure
            }
        
        frontend_data = transform_ai_data_for_frontend(comprehensive_analysis)
        
        # Prepare comprehensive analysis results
        enhanced_results = {
            "analysis_type": "ai_enhanced",
            "ai_analysis": comprehensive_analysis,
            "enhanced_mcp_score": enhanced_mcp_score,
            "generation_ready": True,
            "tool_suggestions_available": True,
            "business_logic_analyzed": True,
            # Add frontend-compatible data
            **frontend_data,
            # Add MCP suggestions API compatibility
            "ai_suggestions": comprehensive_analysis.get("mcp_suggestions", {}),
            "business_analysis": comprehensive_analysis.get("comprehensive_analysis", {}),
            "confidence_score": comprehensive_analysis.get("confidence_score", 0.0),
            "implementation_complexity": comprehensive_analysis.get("implementation_complexity", {})
        }
        
        # Update analysis record with enhanced results
        analysis.mcp_feasibility_score = enhanced_mcp_score
        analysis.analysis_results = enhanced_results
        analysis.status = "completed"
        analysis.completed_at = datetime.utcnow()
        db.commit()
        
        # Complete (100%)
        self.update_state(
            state='SUCCESS',
            meta={
                'current': 100, 
                'total': 100, 
                'status': 'AI-powered analysis completed! MCP tool suggestions available.'
            }
        )
        
        logger.info(f"Enhanced analysis {analysis_id} completed with MCP score: {enhanced_mcp_score}")
        
        return {
            "analysis_id": analysis_id,
            "status": "completed",
            "analysis_type": "ai_enhanced",
            "mcp_score": enhanced_mcp_score,
            "tools_suggested": comprehensive_analysis.get("mcp_suggestions", {}).get("total_tools_suggested", 0),
            "confidence_score": comprehensive_analysis.get("confidence_score", 0.0),
            "ready_for_generation": True,
            "business_domain": comprehensive_analysis.get("comprehensive_analysis", {}).get("business_logic", {}).get("primary_domain", "unknown")
        }
        
    except Exception as exc:
        logger.error(f"Enhanced analysis {analysis_id} failed: {str(exc)}")
        
        # Update analysis status to failed
        try:
            analysis = db.query(RepoAnalysis).filter(RepoAnalysis.id == analysis_id).first()
            if analysis:
                analysis.status = "failed"
                analysis.error_message = str(exc)
                db.commit()
        except:
            pass
        
        # Update task state
        self.update_state(
            state='FAILURE',
            meta={'current': 0, 'total': 100, 'status': f'Enhanced analysis failed: {str(exc)}'}
        )
        
        raise exc
    
    finally:
        db.close()


def calculate_enhanced_mcp_score(comprehensive_analysis: Dict[str, Any]) -> float:
    """
    Calculate enhanced MCP feasibility score based on AI analysis
    """
    try:
        score = 0.0
        
        # Get analysis components
        business_logic = comprehensive_analysis.get("comprehensive_analysis", {}).get("business_logic", {})
        workflows = comprehensive_analysis.get("comprehensive_analysis", {}).get("workflows", {})
        api_capabilities = comprehensive_analysis.get("comprehensive_analysis", {}).get("api_capabilities", {})
        integration_opportunities = comprehensive_analysis.get("comprehensive_analysis", {}).get("integration_opportunities", {})
        mcp_suggestions = comprehensive_analysis.get("mcp_suggestions", {})
        
        # 1. Business Logic Quality (25 points)
        core_operations = business_logic.get("core_operations", [])
        if len(core_operations) > 0:
            score += min(len(core_operations) * 3, 15)  # Up to 15 points for operations
        
        if business_logic.get("primary_domain") != "unknown":
            score += 5  # 5 points for clear domain identification
        
        if len(business_logic.get("use_cases", [])) > 0:
            score += 5  # 5 points for clear use cases
        
        # 2. API and Integration Capabilities (25 points)
        existing_apis = api_capabilities.get("existing_apis", [])
        score += min(len(existing_apis) * 2, 10)  # Up to 10 points for existing APIs
        
        potential_apis = api_capabilities.get("potential_new_apis", [])
        score += min(len(potential_apis) * 1, 5)  # Up to 5 points for potential APIs
        
        external_integrations = integration_opportunities.get("external_services", [])
        score += min(len(external_integrations) * 2, 10)  # Up to 10 points for integrations
        
        # 3. Workflow Automation Potential (20 points)
        automation_opportunities = workflows.get("automation_opportunities", [])
        score += min(len(automation_opportunities) * 2, 10)  # Up to 10 points for automation
        
        business_processes = workflows.get("business_processes", [])
        score += min(len(business_processes) * 1, 10)  # Up to 10 points for processes
        
        # 4. MCP Tool Suggestions Quality (20 points)
        total_tools = mcp_suggestions.get("total_tools_suggested", 0)
        score += min(total_tools * 0.5, 15)  # Up to 15 points for tool variety
        
        quick_wins = len(mcp_suggestions.get("implementation_roadmap", {}).get("phase_1_quick_wins", []))
        if quick_wins > 0:
            score += 5  # 5 points for having quick wins
        
        # 5. Implementation Feasibility (10 points)
        complexity = comprehensive_analysis.get("implementation_complexity", {})
        total_effort = complexity.get("total_estimated_hours", 200)
        
        if total_effort <= 40:
            score += 10  # Very feasible
        elif total_effort <= 80:
            score += 7   # Moderately feasible
        elif total_effort <= 120:
            score += 5   # Challenging but doable
        else:
            score += 2   # Complex implementation
        
        # Normalize to 0-100 range
        final_score = min(max(score, 0.0), 100.0)
        
        logger.info(f"Enhanced MCP score calculated: {final_score}")
        return final_score
        
    except Exception as e:
        logger.error(f"Error calculating enhanced MCP score: {str(e)}")
        # Return a reasonable default score if calculation fails
        return 50.0


@celery_app.task(bind=True, max_retries=1)
def generate_mcp_suggestions_only(self, analysis_id: int, github_token: str):
    """
    Generate MCP tool suggestions for existing analysis
    """
    db = SessionLocal()
    try:
        # Get analysis record
        analysis = db.query(RepoAnalysis).filter(RepoAnalysis.id == analysis_id).first()
        if not analysis:
            raise ValueError(f"Analysis {analysis_id} not found")
        
        if not settings.anthropic_api_key and not settings.openai_api_key:
            raise ValueError("No AI API key configured (neither Claude nor OpenAI)")
        
        # Update status
        analysis.status = "analyzing"
        db.commit()
        
        self.update_state(
            state='PROGRESS',
            meta={'current': 20, 'total': 100, 'status': 'Generating MCP tool suggestions...'}
        )
        
        intelligent_service = IntelligentAnalysisService()
        
        # Generate suggestions based on existing basic analysis
        existing_results = analysis.analysis_results or {}
        
        # Create enhanced analysis context
        enhanced_context = {
            "repository_info": {
                "name": analysis.repo_name,
                "owner": analysis.repo_owner,
                "description": existing_results.get("repository_info", {}).get("description", ""),
                "language": existing_results.get("repository_info", {}).get("language", ""),
                "topics": existing_results.get("repository_info", {}).get("topics", [])
            },
            "existing_analysis": existing_results
        }
        
        # Gather fresh repository content for better suggestions
        comprehensive_analysis = asyncio.run(intelligent_service.analyze_repository_comprehensively(
            analysis.repo_owner,
            analysis.repo_name,
            github_token
        ))
        
        # Update analysis with new suggestions
        enhanced_results = existing_results.copy()

        # Extract MCP suggestions from comprehensive analysis
        # The comprehensive analysis returns the structure directly
        mcp_suggestions = {
            "categories": comprehensive_analysis.get("categories", {}),
            "prioritized_recommendations": comprehensive_analysis.get("prioritized_recommendations", []),
            "implementation_roadmap": comprehensive_analysis.get("implementation_roadmap", {}),
            "total_tools_suggested": comprehensive_analysis.get("total_tools_suggested", 0),
            "repository_analysis": comprehensive_analysis.get("repository_analysis", {}),
            "tool_generation_strategy": comprehensive_analysis.get("tool_generation_strategy", {})
        }

        # Extract business analysis
        business_analysis = {
            "business_logic": comprehensive_analysis.get("business_logic", {}),
            "workflows": comprehensive_analysis.get("workflows", {}),
            "api_capabilities": comprehensive_analysis.get("api_capabilities", {}),
            "integration_opportunities": comprehensive_analysis.get("integration_opportunities", {})
        }

        enhanced_results.update({
            "ai_suggestions": mcp_suggestions,
            "business_analysis": business_analysis,
            "suggestions_generated": True,
            "generation_ready": True
        })
        
        analysis.analysis_results = enhanced_results
        analysis.status = "completed"
        db.commit()
        
        self.update_state(
            state='SUCCESS',
            meta={'current': 100, 'total': 100, 'status': 'MCP tool suggestions generated successfully!'}
        )
        
        return {
            "analysis_id": analysis_id,
            "status": "completed",
            "suggestions_count": comprehensive_analysis.get("total_tools_suggested", 0),
            "ready_for_selection": True
        }
        
    except Exception as exc:
        logger.error(f"MCP suggestions generation failed for {analysis_id}: {str(exc)}")
        
        try:
            analysis = db.query(RepoAnalysis).filter(RepoAnalysis.id == analysis_id).first()
            if analysis:
                analysis.status = "failed"
                analysis.error_message = f"Suggestions generation failed: {str(exc)}"
                db.commit()
        except:
            pass
        
        self.update_state(
            state='FAILURE',
            meta={'current': 0, 'total': 100, 'status': f'Suggestions generation failed: {str(exc)}'}
        )
        
        raise exc
    
    finally:
        db.close()