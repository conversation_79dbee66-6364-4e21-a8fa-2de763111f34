"""
Intelligent Analysis Service using AI (OpenAI/Claude) for comprehensive repository business logic analysis
"""
import json
import logging
import asyncio
from typing import Dict, List, Any, Optional
from openai import Async<PERSON>penAI
import anthropic
from ..config import settings
from .github_service import GitHubService

logger = logging.getLogger(__name__)


class IntelligentAnalysisService:
    """Service for AI-powered comprehensive repository analysis"""
    
    def __init__(self):
        # Initialize AI clients - prefer <PERSON> if available, fallback to OpenAI
        self.anthropic_client = anthropic.AsyncAnthropic(api_key=settings.anthropic_api_key) if settings.anthropic_api_key else None
        self.openai_client = AsyncOpenAI(api_key=settings.openai_api_key) if settings.openai_api_key else None
        self.github_service = GitHubService()
        self.max_files_to_analyze = 50  # Limit for API cost control
        self.max_file_size = 100000  # 100KB per file limit
        
        # Determine which AI service to use
        self.use_claude = bool(settings.anthropic_api_key)
        logger.info(f"Initialized AI service: {'Claude' if self.use_claude else 'OpenAI' if self.openai_client else 'None'}")
    
    async def _call_ai_service(self, prompt: str, max_tokens: int = 4000) -> str:
        """Unified method to call either Claude or OpenAI"""
        if self.use_claude and self.anthropic_client:
            response = await self.anthropic_client.messages.create(
                model="claude-3-5-sonnet-20241022",
                max_tokens=max_tokens,
                messages=[{"role": "user", "content": prompt}]
            )
            return response.content[0].text
        elif self.openai_client:
            response = await self.openai_client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[{"role": "user", "content": prompt}],
                temperature=0.3,
                max_tokens=max_tokens
            )
            return response.choices[0].message.content
        else:
            raise ValueError("No AI service available (neither Claude nor OpenAI API key configured)")
    
    async def _analyze_everything_comprehensive(self, repo_content: Dict[str, Any]) -> Dict[str, Any]:
        """Single comprehensive analysis for Claude (large context window)"""
        
        # Enhanced comprehensive analysis prompt with multi-dimensional framework
        prompt = f"""
# Repository Deep Analysis for MCP Tool Generation

You are an expert MCP (Model Context Protocol) tool architect analyzing a GitHub repository to identify comprehensive tool generation opportunities. Your goal is to extract maximum value from the repository by identifying ALL possible MCP tools that could be built based on the repository's actual capabilities, structure, and domain.

## Repository Information
Repository: {repo_content['repository_info']['name']}
Description: {repo_content['repository_info'].get('description', 'No description')}
Language: {repo_content['repository_info'].get('language', 'Unknown')}
Topics: {repo_content['repository_info'].get('topics', [])}

## Repository Structure:
{json.dumps(repo_content.get('repository_tree', []), indent=2)[:5000]}

## Code Files (key files):
{json.dumps(dict(list(repo_content.get('code_samples', {}).items())[:10]), indent=2)[:10000]}

## Configuration Files:
{json.dumps(repo_content.get('config_files', {}), indent=2)[:3000]}

## Analysis Framework

Conduct a **multi-dimensional analysis** across these key areas:

### 1. Core Functionality Analysis
- **Primary Functions**: What are the main operations this repository performs?
- **Key Algorithms**: What computational or business logic can be exposed as tools?
- **Data Processing**: What data transformations, validations, or manipulations occur?
- **Core Business Logic**: What domain-specific operations could be valuable as tools?

### 2. API & Interface Analysis
- **Existing APIs**: Document all current API endpoints and their capabilities
- **CLI Commands**: Analyze command-line interfaces and their operations
- **Function Exports**: Identify key functions that could be exposed as tools
- **Service Interfaces**: Look for service patterns that could become tools

### 3. Data Flow & Management Analysis
- **Input/Output Patterns**: What data flows through the system?
- **Database Operations**: What CRUD operations or queries are performed?
- **File Operations**: What file manipulations, parsing, or generation occurs?
- **Data Validation**: What validation and sanitization logic exists?

### 4. Integration & Connectivity Analysis
- **External Services**: What third-party APIs or services are integrated?
- **Authentication**: What auth mechanisms could be exposed as tools?
- **Webhooks/Events**: What event-driven capabilities exist?
- **Protocol Support**: What communication protocols are implemented?

### 5. Business Process Analysis
- **Workflows**: What multi-step business processes could be automated?
- **Decision Logic**: What conditional logic or rule engines exist?
- **Reporting**: What data aggregation or reporting capabilities exist?
- **Monitoring**: What health checks, metrics, or diagnostics are available?

### 6. Utility & Helper Analysis
- **Utility Functions**: What helper functions could be useful as standalone tools?
- **Formatters**: What data formatting, parsing, or conversion utilities exist?
- **Validators**: What validation functions could be exposed?
- **Generators**: What code, config, or content generation capabilities exist?

### 7. Configuration & Management Analysis
- **Config Management**: What configuration operations could be exposed?
- **Environment Setup**: What setup or initialization processes exist?
- **Deployment**: What deployment or infrastructure management capabilities exist?
- **Testing**: What testing utilities or quality assurance tools exist?

## Tool Generation Guidelines

### Tool Quantity Scaling
- **Simple repositories (basic libraries)**: 3-8 tools
- **Medium complexity (applications/frameworks)**: 8-15 tools
- **Complex repositories (platforms/ecosystems)**: 15-30+ tools
- **Enterprise systems**: 20-50+ tools

### Tool Categories to Consider
Generate tools across these categories based on repository analysis:

1. **CORE_OPERATIONS** - Primary business logic functions, key algorithmic operations, data processing workflows
2. **DATA_MANAGEMENT** - CRUD operations, data validation and transformation, query and search capabilities, import/export functions  
3. **INTEGRATION** - API interaction tools, authentication helpers, external service connectors, webhook handlers
4. **UTILITIES** - Formatting and parsing tools, validation utilities, code generators, configuration helpers
5. **MONITORING** - Health check tools, performance monitoring, log analysis, error diagnostics
6. **AUTOMATION** - Workflow automation, deployment helpers, testing utilities, environment setup
7. **REPORTING** - Data aggregation, report generation, metrics calculation, visualization helpers

### Tool Design Principles
For each tool, ensure:
1. **Specific Purpose**: Each tool should solve a specific, well-defined problem
2. **Clear Inputs/Outputs**: Define precise input schemas and expected outputs
3. **Error Handling**: Include comprehensive error scenarios and handling
4. **Business Value**: Articulate clear business value and use cases
5. **Implementation Feasibility**: Ensure tools can actually be built from the repository code
6. **User Experience**: Design tools to be intuitive and useful for Claude users

## Required JSON Output Format

{{
    "repository_analysis": {{
        "complexity_score": "1-100 based on code complexity, features, and depth",
        "primary_domain": "specific domain like 'document processing', 'web scraping', 'data analysis'",
        "key_capabilities": ["capability1", "capability2", "..."],
        "technical_stack": ["tech1", "tech2", "..."],
        "integration_points": ["integration1", "integration2", "..."],
        "business_processes": ["process1", "process2", "..."]
    }},
    "tool_generation_strategy": {{
        "total_tools_recommended": "number based on complexity (3-50+ tools)",
        "tool_distribution": {{
            "core_operations": "number",
            "data_management": "number", 
            "integration": "number",
            "utilities": "number",
            "monitoring": "number",
            "automation": "number",
            "reporting": "number"
        }}
    }},
    "categories": {{
        "CORE_OPERATIONS": [
            {{
                "tool_name": "specific_operation_tool",
                "description": "detailed description of what this tool does",
                "business_value": "why this tool is valuable for Claude users",
                "complexity_level": "low|medium|high",
                "input_schema": {{
                    "type": "object",
                    "properties": {{ "param1": {{"type": "string", "description": "what this parameter does"}}, "param2": {{"type": "number", "description": "numeric input"}} }},
                    "required": ["param1"]
                }},
                "output_schema": {{
                    "type": "object",
                    "properties": {{ "result": {{"type": "string", "description": "what the output contains"}}, "status": {{"type": "string", "description": "operation status"}} }}
                }},
                "implementation_hints": "specific guidance on how to implement this tool from the repository code",
                "use_cases": ["specific use case 1", "specific use case 2", "..."],
                "dependencies": ["dependency1", "dependency2", "..."],
                "error_scenarios": ["error scenario 1", "error scenario 2", "..."],
                "estimated_effort_hours": "realistic development time estimate"
            }}
        ],
        "DATA_MANAGEMENT": [...],
        "INTEGRATION": [...], 
        "UTILITIES": [...],
        "MONITORING": [...],
        "AUTOMATION": [...],
        "REPORTING": [...]
    }},
    "prioritized_recommendations": [
        {{
            "priority": 1,
            "tool_name": "highest_value_tool",
            "justification": "detailed explanation why this should be implemented first",
            "estimated_impact": "high|medium|low",
            "business_value_score": "1-100"
        }}
    ],
    "implementation_roadmap": {{
        "phase_1_tools": ["quick win tool names that are low complexity but high value"],
        "phase_2_tools": ["core functionality tools that provide main business value"],
        "phase_3_tools": ["advanced tools that provide specialized capabilities"]
    }},
    "business_logic": {{
        "primary_domain": "specific domain identification",
        "business_purpose": "clear description of what this repository does from a business perspective",
        "core_operations": ["main business operations this repository performs"],
        "business_entities": ["key data entities or business objects"],
        "business_rules": ["important business rules or constraints"],
        "target_users": ["types of users who would benefit from these tools"],
        "use_cases": [
            {{
                "scenario": "realistic business scenario",
                "user_story": "as a user, I want to...",
                "business_outcome": "expected business result or benefit"
            }}
        ],
        "value_proposition": "core business value this repository provides"
    }},
    "workflows": {{
        "data_flows": ["step1 -> step2 -> step3 descriptions of how data moves through the system"],
        "business_processes": ["end-to-end business processes supported"],
        "integration_patterns": ["how this repository integrates with other systems"],
        "automation_opportunities": ["processes that could be automated through MCP tools"]
    }},
    "api_capabilities": {{
        "existing_apis": [
            {{
                "endpoint": "/api/specific-endpoint",
                "method": "GET|POST|PUT|DELETE",
                "purpose": "what this endpoint accomplishes",
                "parameters": ["param1", "param2"],
                "response_format": "description of response structure"
            }}
        ],
        "api_patterns": ["REST", "GraphQL", "WebSocket", "etc"],
        "authentication": {{
            "methods": ["JWT", "OAuth", "API Key", "etc"],
            "description": "how authentication is handled"
        }},
        "external_integrations": ["specific external services or APIs used"],
        "potential_new_apis": [
            {{
                "endpoint": "/api/proposed-endpoint",
                "purpose": "potential new functionality that could be exposed",
                "business_value": "why this would be valuable as an MCP tool"
            }}
        ]
    }},
    "integration_opportunities": {{
        "current_integrations": ["existing third-party integrations"],
        "data_management": {{
            "databases": ["specific database types used"],
            "data_formats": ["JSON", "XML", "CSV", "specific formats handled"],
            "storage_patterns": ["how data is stored and retrieved"]
        }},
        "external_services": ["third-party services this repository connects to"],
        "integration_opportunities": ["new integration possibilities for MCP tools"],
        "data_transformations": ["types of data transformations performed"]
    }},
    "total_tools_suggested": "actual count of all tools across all categories",
    "estimated_total_effort_hours": "realistic total estimate for implementing all suggested tools"
}}

## Critical Instructions

1. **Be Comprehensive**: Analyze the repository thoroughly and suggest ALL viable tools based on actual capabilities. Don't limit to just 3-5 tools.

2. **Be Specific**: Each tool should be directly derived from actual repository capabilities, functions, and features - not generic suggestions.

3. **Be Practical**: Every tool should be implementable using the repository's existing code, functions, and infrastructure.

4. **Be Valuable**: Each tool should solve real problems and provide clear business value for Claude users.

5. **Scale Appropriately**: Complex repositories should generate 15-30+ tools; simple ones should generate 5-8 highly relevant tools.

6. **Consider Context**: Think about how these tools would actually be used by Claude users in real-world scenarios.

7. **Technical Depth**: Dive deep into the actual code structure, functions, and capabilities - not just README descriptions.

## Analysis Depth Example

For a complex repository like a payment processing library, you should identify tools for:
- Payment processing operations (charge, refund, capture)
- Customer management (create, update, retrieve, delete)
- Subscription handling (create, modify, cancel, billing)
- Invoice generation and management
- Webhook verification and processing
- Transaction monitoring and reporting
- Dispute management and handling
- Balance and payout operations
- Tax calculation and compliance
- Authentication and security helpers
- Error handling and retry utilities
- Data validation and formatting tools
- Reporting and analytics aggregators
- Configuration and setup tools
- Testing and debugging utilities
etc. (20-30+ tools total for complex systems)

Remember: The goal is to unlock the full potential of the repository as a comprehensive MCP tool suite, creating tools that provide genuine value and leverage the unique capabilities of the codebase.
"""
        
        try:
            content = await self._call_ai_service(prompt, max_tokens=8000)
            content = content.strip()
            
            # Extract JSON from response
            if content.startswith('```json'):
                content = content.split('```json')[1].split('```')[0].strip()
            elif content.startswith('```'):
                content = content.split('```')[1].split('```')[0].strip()
            
            result = json.loads(content)
            logger.info("Comprehensive analysis completed successfully")
            return result
            
        except Exception as e:
            logger.error(f"Comprehensive analysis failed: {str(e)}")
            # Return enhanced empty structure if analysis fails
            return {
                "repository_analysis": {
                    "complexity_score": 0,
                    "primary_domain": "unknown",
                    "key_capabilities": [],
                    "technical_stack": [],
                    "integration_points": [],
                    "business_processes": []
                },
                "tool_generation_strategy": {
                    "total_tools_recommended": 0,
                    "tool_distribution": {
                        "core_operations": 0,
                        "data_management": 0,
                        "integration": 0,
                        "utilities": 0,
                        "monitoring": 0,
                        "automation": 0,
                        "reporting": 0
                    }
                },
                "categories": {
                    "CORE_OPERATIONS": [],
                    "DATA_MANAGEMENT": [],
                    "INTEGRATION": [],
                    "UTILITIES": [],
                    "MONITORING": [],
                    "AUTOMATION": [],
                    "REPORTING": []
                },
                "prioritized_recommendations": [],
                "implementation_roadmap": {
                    "phase_1_tools": [],
                    "phase_2_tools": [],
                    "phase_3_tools": []
                },
                "business_logic": {
                    "primary_domain": "unknown",
                    "business_purpose": "Analysis failed",
                    "core_operations": [],
                    "business_entities": [],
                    "business_rules": [],
                    "target_users": [],
                    "use_cases": [],
                    "value_proposition": "Could not determine"
                },
                "workflows": {
                    "data_flows": [],
                    "business_processes": [],
                    "integration_patterns": [],
                    "automation_opportunities": []
                },
                "api_capabilities": {
                    "existing_apis": [],
                    "api_patterns": [],
                    "authentication": {"methods": [], "description": ""},
                    "external_integrations": [],
                    "potential_new_apis": []
                },
                "integration_opportunities": {
                    "current_integrations": [],
                    "data_management": {"databases": [], "data_formats": [], "storage_patterns": []},
                    "external_services": [],
                    "integration_opportunities": [],
                    "data_transformations": []
                },
                "total_tools_suggested": 0,
                "estimated_total_effort_hours": 0
            }
        
    async def analyze_repository_comprehensively(
        self, 
        repo_owner: str,
        repo_name: str,
        github_token: str,
        repo_info: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        Perform comprehensive business logic analysis using OpenAI
        """
        try:
            logger.info(f"Starting comprehensive analysis for {repo_owner}/{repo_name}")
            
            # 1. Gather comprehensive repository content
            repo_content = await self._gather_repository_content(
                repo_owner, repo_name, github_token, repo_info
            )
            
            # Add small delay to make progress visible
            await asyncio.sleep(1)
            
            # 2-6. Perform analysis (single call for Claude, multiple for OpenAI)
            if self.use_claude:
                # Use comprehensive single-call analysis for Claude
                comprehensive_results = await self._analyze_everything_comprehensive(repo_content)
                # Add delay to show AI processing time
                await asyncio.sleep(2)
                business_analysis = comprehensive_results.get("business_logic", {})
                workflow_analysis = comprehensive_results.get("workflows", {}) 
                api_analysis = comprehensive_results.get("api_capabilities", {})
                integration_analysis = comprehensive_results.get("integration_opportunities", {})
                mcp_suggestions = comprehensive_results.get("mcp_suggestions", {})
            else:
                # For OpenAI, use separate calls with delays (rate limiting)
                business_analysis = await self._analyze_business_logic(repo_content)
                await asyncio.sleep(22)  # Rate limit: 3 requests/minute = 20s between calls + buffer
                
                workflow_analysis = await self._analyze_workflows(repo_content)
                await asyncio.sleep(22)
                
                api_analysis = await self._analyze_api_capabilities(repo_content)
                await asyncio.sleep(22)
                
                integration_analysis = await self._analyze_integration_points(repo_content)
                await asyncio.sleep(22)
                
                mcp_suggestions = await self._generate_mcp_suggestions({
                    "repository_info": repo_content["repository_info"],
                    "business_logic": business_analysis,
                    "workflows": workflow_analysis,
                    "apis": api_analysis,
                    "integrations": integration_analysis,
                    "code_samples": repo_content["code_samples"]
                })
            
            # 7. Calculate confidence and complexity scores
            confidence_score = self._calculate_analysis_confidence(mcp_suggestions)
            complexity_assessment = self._assess_implementation_complexity(mcp_suggestions)
            
            return {
                "comprehensive_analysis": {
                    "business_logic": business_analysis,
                    "workflows": workflow_analysis,
                    "api_capabilities": api_analysis,
                    "integration_opportunities": integration_analysis
                },
                "mcp_suggestions": mcp_suggestions,
                "confidence_score": confidence_score,
                "implementation_complexity": complexity_assessment,
                "analysis_metadata": {
                    "files_analyzed": repo_content.get("files_count", 0),
                    "analysis_timestamp": repo_content.get("analysis_timestamp"),
                    "repository_size": repo_content.get("repository_size", 0)
                },
                "repository_tree": repo_content.get("repository_tree", []),
                "repository_info": repo_content.get("repository_info", {})
            }
            
        except Exception as e:
            logger.error(f"Comprehensive analysis failed for {repo_owner}/{repo_name}: {str(e)}")
            raise Exception(f"AI-powered analysis failed: {str(e)}")
    
    async def _gather_repository_content(
        self, 
        repo_owner: str, 
        repo_name: str, 
        github_token: str,
        repo_info: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Gather comprehensive repository content for analysis"""
        
        # Get basic repository info if not provided
        if not repo_info:
            repo_info = self.github_service.get_repository_info_sync(
                repo_owner, repo_name, github_token
            )
        
        # Get repository tree and important files
        repo_tree = self.github_service.get_repository_tree_sync(
            repo_owner, repo_name, github_token, max_depth=3
        )
        
        # Identify key files to analyze
        important_files = self._identify_important_files(repo_tree)
        
        # Get file contents for analysis
        code_samples = {}
        files_analyzed = 0
        
        for file_path in important_files[:self.max_files_to_analyze]:
            try:
                file_content = self.github_service.get_file_content_sync(
                    repo_owner, repo_name, file_path, github_token
                )
                
                if file_content and len(file_content) <= self.max_file_size:
                    code_samples[file_path] = file_content
                    files_analyzed += 1
                    
            except Exception as e:
                logger.warning(f"Failed to get content for {file_path}: {str(e)}")
                continue
        
        # Get nested tree structure for frontend display
        nested_tree = self.github_service.get_repository_tree_nested(
            repo_owner, repo_name, github_token, max_depth=3
        )
        
        return {
            "repository_info": repo_info,
            "repository_tree": nested_tree,
            "code_samples": code_samples,
            "files_count": files_analyzed,
            "repository_size": repo_info.get("size", 0),
            "analysis_timestamp": repo_info.get("updated_at")
        }
    
    def _identify_important_files(self, repo_tree: List[Dict]) -> List[str]:
        """Identify important files for business logic analysis"""
        
        important_patterns = [
            # Configuration and setup files
            "README.md", "setup.py", "pyproject.toml", "package.json", 
            "Cargo.toml", "go.mod", "pom.xml", "build.gradle",
            
            # Main application files
            "main.py", "app.py", "index.js", "server.js", "main.go",
            "__init__.py", "cli.py", "command.py",
            
            # API and service files
            "api/", "routes/", "handlers/", "controllers/", "services/",
            "endpoints/", "views/", "models/",
            
            # Documentation
            "docs/", "documentation/", "examples/", "CHANGELOG",
            
            # Configuration
            "config/", "settings/", "env", ".env.example"
        ]
        
        important_files = []
        
        for item in repo_tree:
            file_path = item.get("path", "")
            file_name = file_path.split("/")[-1]
            
            # Check if file matches important patterns
            for pattern in important_patterns:
                if (pattern in file_path.lower() or 
                    pattern in file_name.lower() or
                    file_path.startswith(pattern) or
                    file_name == pattern):
                    important_files.append(file_path)
                    break
        
        # Sort by importance (main files first, then config, then others)
        def file_importance(file_path):
            main_files = ["main.py", "app.py", "index.js", "server.js", "README.md"]
            if any(main in file_path.lower() for main in main_files):
                return 0
            elif "api" in file_path.lower() or "route" in file_path.lower():
                return 1
            elif "config" in file_path.lower() or "setup" in file_path.lower():
                return 2
            else:
                return 3
        
        important_files.sort(key=file_importance)
        return important_files
    
    async def _analyze_business_logic(self, repo_content: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze repository's core business logic"""
        
        prompt = f"""
        Analyze this repository's core business logic and purpose:

        Repository Info:
        - Name: {repo_content['repository_info'].get('name')}
        - Description: {repo_content['repository_info'].get('description')}
        - Language: {repo_content['repository_info'].get('language')}
        - Topics: {repo_content['repository_info'].get('topics', [])}

        Key Files Content:
        {json.dumps(dict(list(repo_content['code_samples'].items())[:5]), indent=2)}

        Please analyze and identify:
        1. Primary business domain (e.g., document processing, data analysis, web scraping, etc.)
        2. Core business operations and their purposes
        3. Key business entities and data models
        4. Business rules and logic patterns
        5. Value proposition - what business problem does this solve?
        6. Target users and use cases
        7. Business processes and workflows

        Return as JSON with this structure:
        {{
            "primary_domain": "string",
            "business_purpose": "detailed description",
            "core_operations": [
                {{
                    "name": "operation_name",
                    "purpose": "what it does for business",
                    "business_value": "why it matters",
                    "complexity": "low|medium|high"
                }}
            ],
            "business_entities": ["entity1", "entity2"],
            "business_rules": ["rule1", "rule2"],
            "target_users": ["user_type1", "user_type2"],
            "use_cases": [
                {{
                    "scenario": "business scenario",
                    "user_story": "as a user, I want to...",
                    "business_outcome": "expected result"
                }}
            ],
            "value_proposition": "core business value"
        }}
        """
        
        try:
            content = await self._call_ai_service(prompt, max_tokens=2000)
            content = content.strip()
            
            # Try to extract JSON from the response if it contains other text
            if content.startswith('```json'):
                content = content.split('```json')[1].split('```')[0].strip()
            elif content.startswith('```'):
                content = content.split('```')[1].split('```')[0].strip()
            
            return json.loads(content)
            
        except Exception as e:
            logger.error(f"Business logic analysis failed: {str(e)}")
            return {
                "primary_domain": "unknown",
                "business_purpose": "Analysis failed",
                "core_operations": [],
                "business_entities": [],
                "business_rules": [],
                "target_users": [],
                "use_cases": [],
                "value_proposition": "Could not determine"
            }
    
    async def _analyze_workflows(self, repo_content: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze workflow patterns and business processes"""
        
        prompt = f"""
        Analyze the workflow patterns and business processes in this repository:

        Code Samples:
        {json.dumps(dict(list(repo_content['code_samples'].items())[:8]), indent=2)}

        Identify:
        1. Data flow patterns (input → processing → output)
        2. Business process workflows
        3. Integration points with external systems
        4. Automation opportunities
        5. Decision points and business rules
        6. Error handling and recovery processes

        Return as JSON:
        {{
            "data_flows": [
                {{
                    "name": "flow_name",
                    "description": "what the flow does",
                    "inputs": ["input1", "input2"],
                    "processing_steps": ["step1", "step2"],
                    "outputs": ["output1", "output2"],
                    "business_impact": "why this flow matters"
                }}
            ],
            "business_processes": [
                {{
                    "process_name": "name",
                    "description": "what it accomplishes",
                    "steps": ["step1", "step2"],
                    "automation_level": "manual|semi-automated|automated",
                    "improvement_opportunities": ["opportunity1"]
                }}
            ],
            "integration_patterns": [
                {{
                    "integration_type": "API|Database|File|etc",
                    "description": "what it integrates with",
                    "data_exchanged": ["data_type1"],
                    "frequency": "real-time|batch|on-demand"
                }}
            ],
            "automation_opportunities": [
                {{
                    "opportunity": "what could be automated",
                    "current_state": "how it works now",
                    "potential_benefit": "business value of automation",
                    "complexity": "low|medium|high"
                }}
            ]
        }}
        """
        
        try:
            content = await self._call_ai_service(prompt, max_tokens=2000)
            content = content.strip()
            
            # Try to extract JSON from the response if it contains other text
            if content.startswith('```json'):
                content = content.split('```json')[1].split('```')[0].strip()
            elif content.startswith('```'):
                content = content.split('```')[1].split('```')[0].strip()
            
            return json.loads(content)
            
        except Exception as e:
            logger.error(f"Workflow analysis failed: {str(e)}")
            return {
                "data_flows": [],
                "business_processes": [],
                "integration_patterns": [],
                "automation_opportunities": []
            }
    
    async def _analyze_api_capabilities(self, repo_content: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze API patterns and capabilities"""
        
        prompt = f"""
        Analyze the API capabilities and patterns in this repository:

        Code Samples:
        {json.dumps(dict(list(repo_content['code_samples'].items())[:10]), indent=2)}

        Identify:
        1. Existing API endpoints and their purposes
        2. API design patterns used
        3. Authentication and authorization methods
        4. Data formats and schemas
        5. Rate limiting and performance considerations
        6. External API integrations
        7. Potential new API endpoints that could be created

        Return as JSON:
        {{
            "existing_apis": [
                {{
                    "endpoint": "/api/endpoint",
                    "method": "GET|POST|PUT|DELETE",
                    "purpose": "what it does",
                    "input_schema": {{"param": "type"}},
                    "output_schema": {{"result": "type"}},
                    "business_function": "business purpose"
                }}
            ],
            "api_patterns": [
                {{
                    "pattern_name": "REST|GraphQL|RPC|etc",
                    "usage": "how it's used",
                    "benefits": "advantages for business"
                }}
            ],
            "authentication": {{
                "methods": ["API_KEY", "OAuth", "JWT"],
                "description": "how auth works"
            }},
            "external_integrations": [
                {{
                    "service": "external service name",
                    "purpose": "why it's integrated",
                    "api_type": "REST|GraphQL|etc",
                    "data_exchanged": ["data_type1"]
                }}
            ],
            "potential_new_apis": [
                {{
                    "proposed_endpoint": "/api/new-endpoint",
                    "purpose": "business need it would serve",
                    "value_proposition": "why create this API",
                    "complexity": "low|medium|high"
                }}
            ]
        }}
        """
        
        try:
            content = await self._call_ai_service(prompt, max_tokens=2000)
            content = content.strip()
            
            # Try to extract JSON from the response if it contains other text
            if content.startswith('```json'):
                content = content.split('```json')[1].split('```')[0].strip()
            elif content.startswith('```'):
                content = content.split('```')[1].split('```')[0].strip()
            
            return json.loads(content)
            
        except Exception as e:
            logger.error(f"API analysis failed: {str(e)}")
            return {
                "existing_apis": [],
                "api_patterns": [],
                "authentication": {"methods": [], "description": ""},
                "external_integrations": [],
                "potential_new_apis": []
            }
    
    async def _analyze_integration_points(self, repo_content: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze integration opportunities and system connections"""
        
        prompt = f"""
        Analyze integration opportunities and system connection points:

        Repository: {repo_content['repository_info'].get('name')}
        Code Analysis:
        {json.dumps(dict(list(repo_content['code_samples'].items())[:8]), indent=2)}

        Identify:
        1. Current system integrations
        2. Database connections and data management
        3. File system operations
        4. Network communications
        5. Third-party service integrations
        6. Potential new integration opportunities
        7. Data transformation and ETL processes

        Return as JSON:
        {{
            "current_integrations": [
                {{
                    "integration_name": "name",
                    "type": "Database|API|File|Service",
                    "purpose": "business reason",
                    "data_flow": "bidirectional|input|output",
                    "criticality": "high|medium|low"
                }}
            ],
            "data_management": {{
                "databases": ["db_type1", "db_type2"],
                "data_formats": ["JSON", "CSV", "XML"],
                "storage_patterns": ["pattern1", "pattern2"]
            }},
            "external_services": [
                {{
                    "service": "service_name",
                    "purpose": "why it's used",
                    "integration_method": "API|SDK|Direct",
                    "business_dependency": "high|medium|low"
                }}
            ],
            "integration_opportunities": [
                {{
                    "opportunity": "potential integration",
                    "business_value": "what business value it would provide",
                    "technical_approach": "how to implement",
                    "effort_required": "low|medium|high",
                    "roi_potential": "high|medium|low"
                }}
            ],
            "data_transformations": [
                {{
                    "transformation": "what data is transformed",
                    "from_format": "source format",
                    "to_format": "target format",
                    "business_purpose": "why transformation is needed"
                }}
            ]
        }}
        """
        
        try:
            content = await self._call_ai_service(prompt, max_tokens=2000)
            content = content.strip()
            
            # Try to extract JSON from the response if it contains other text
            if content.startswith('```json'):
                content = content.split('```json')[1].split('```')[0].strip()
            elif content.startswith('```'):
                content = content.split('```')[1].split('```')[0].strip()
            
            return json.loads(content)
            
        except Exception as e:
            logger.error(f"Integration analysis failed: {str(e)}")
            return {
                "current_integrations": [],
                "data_management": {"databases": [], "data_formats": [], "storage_patterns": []},
                "external_services": [],
                "integration_opportunities": [],
                "data_transformations": []
            }
    
    async def _generate_mcp_suggestions(self, analysis_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate categorized MCP tool suggestions based on comprehensive analysis"""
        
        prompt = f"""
        Based on this comprehensive repository analysis, suggest specific MCP tools that would provide genuine business value:

        Analysis Data:
        {json.dumps(analysis_data, indent=2)}

        Generate MCP tool suggestions in these categories:
        1. CORE_BUSINESS_TOOLS: Direct business logic operations
        2. WORKFLOW_AUTOMATION: Process automation and orchestration  
        3. DATA_INTEGRATION: Data transformation and system integration
        4. ANALYTICS_INTELLIGENCE: Business intelligence and reporting
        5. COMPLIANCE_SECURITY: Compliance, audit, and security tools
        6. OPTIMIZATION_PERFORMANCE: Performance and cost optimization
        7. USER_EXPERIENCE: End-user interaction and interface tools

        For each tool, provide:
        - tool_name: Clear, descriptive name (snake_case)
        - category: Which category it belongs to
        - business_value: Why this tool would be valuable for AI assistants
        - complexity_level: low/medium/high implementation complexity
        - input_schema: Expected input parameters with types
        - output_schema: Expected output structure with types
        - implementation_hints: Key technical considerations for building this
        - use_cases: Specific scenarios where AI assistants would use this
        - dependencies: What from the original repo this tool would depend on
        - estimated_effort_hours: Realistic development time estimate

        Focus on tools that:
        - Solve real business problems identified in the analysis
        - Would be frequently used by AI assistants
        - Leverage the unique capabilities of this repository
        - Provide clear, measurable business value

        Return as valid JSON:
        {{
            "categories": {{
                "CORE_BUSINESS_TOOLS": [tool_objects],
                "WORKFLOW_AUTOMATION": [tool_objects],
                "DATA_INTEGRATION": [tool_objects],
                "ANALYTICS_INTELLIGENCE": [tool_objects],
                "COMPLIANCE_SECURITY": [tool_objects],
                "OPTIMIZATION_PERFORMANCE": [tool_objects],
                "USER_EXPERIENCE": [tool_objects]
            }},
            "prioritized_recommendations": [
                {{
                    "tool_name": "highest_value_tool",
                    "priority_score": 95,
                    "reasoning": "why this is highest priority"
                }}
            ],
            "implementation_roadmap": {{
                "phase_1_quick_wins": [
                    {{
                        "tool_name": "easy_tool",
                        "effort_hours": 8,
                        "business_impact": "high|medium|low"
                    }}
                ],
                "phase_2_core_features": [
                    {{
                        "tool_name": "medium_tool", 
                        "effort_hours": 24,
                        "business_impact": "high|medium|low"
                    }}
                ],
                "phase_3_advanced": [
                    {{
                        "tool_name": "complex_tool",
                        "effort_hours": 40,
                        "business_impact": "high|medium|low"
                    }}
                ]
            }},
            "total_tools_suggested": 0,
            "estimated_total_effort_hours": 0
        }}
        """
        
        try:
            content = await self._call_ai_service(prompt, max_tokens=4000)
            content = content.strip()
            
            # Try to extract JSON from the response if it contains other text
            if content.startswith('```json'):
                content = content.split('```json')[1].split('```')[0].strip()
            elif content.startswith('```'):
                content = content.split('```')[1].split('```')[0].strip()
            
            suggestions = json.loads(content)
            
            # Calculate totals
            total_tools = sum(len(tools) for tools in suggestions["categories"].values())
            total_effort = self._calculate_total_effort(suggestions)
            
            suggestions["total_tools_suggested"] = total_tools
            suggestions["estimated_total_effort_hours"] = total_effort
            
            return suggestions
            
        except Exception as e:
            logger.error(f"MCP suggestions generation failed: {str(e)}")
            return {
                "categories": {
                    "CORE_BUSINESS_TOOLS": [],
                    "WORKFLOW_AUTOMATION": [],
                    "DATA_INTEGRATION": [],
                    "ANALYTICS_INTELLIGENCE": [],
                    "COMPLIANCE_SECURITY": [],
                    "OPTIMIZATION_PERFORMANCE": [],
                    "USER_EXPERIENCE": []
                },
                "prioritized_recommendations": [],
                "implementation_roadmap": {
                    "phase_1_quick_wins": [],
                    "phase_2_core_features": [],
                    "phase_3_advanced": []
                },
                "total_tools_suggested": 0,
                "estimated_total_effort_hours": 0
            }
    
    def _calculate_total_effort(self, suggestions: Dict[str, Any]) -> int:
        """Calculate total effort hours from all suggested tools"""
        total_hours = 0
        
        for phase_tools in suggestions.get("implementation_roadmap", {}).values():
            for tool in phase_tools:
                total_hours += tool.get("effort_hours", 0)
                
        return total_hours
    
    def _calculate_analysis_confidence(self, mcp_suggestions: Dict[str, Any]) -> float:
        """Calculate confidence score for the analysis"""
        
        # Base confidence on number of tools suggested and roadmap completeness
        total_tools = mcp_suggestions.get("total_tools_suggested", 0)
        has_roadmap = bool(mcp_suggestions.get("implementation_roadmap", {}).get("phase_1_quick_wins"))
        has_priorities = bool(mcp_suggestions.get("prioritized_recommendations"))
        
        confidence = 0.0
        
        if total_tools > 0:
            confidence += min(total_tools * 0.05, 0.4)  # Up to 40% for having tools
        
        if has_roadmap:
            confidence += 0.3  # 30% for having implementation roadmap
            
        if has_priorities:
            confidence += 0.3  # 30% for having prioritized recommendations
            
        return min(confidence, 1.0)
    
    def _assess_implementation_complexity(self, mcp_suggestions: Dict[str, Any]) -> Dict[str, Any]:
        """Assess overall implementation complexity"""
        
        roadmap = mcp_suggestions.get("implementation_roadmap", {})
        total_effort = mcp_suggestions.get("estimated_total_effort_hours", 0)
        
        # Determine complexity level
        if total_effort <= 40:
            complexity_level = "low"
        elif total_effort <= 120:
            complexity_level = "medium"
        else:
            complexity_level = "high"
        
        return {
            "overall_complexity": complexity_level,
            "total_estimated_hours": total_effort,
            "quick_wins_available": len(roadmap.get("phase_1_quick_wins", [])),
            "advanced_features_count": len(roadmap.get("phase_3_advanced", [])),
            "recommendation": self._get_complexity_recommendation(complexity_level, total_effort)
        }
    
    def _get_complexity_recommendation(self, complexity_level: str, total_effort: int) -> str:
        """Get recommendation based on complexity assessment"""
        
        if complexity_level == "low":
            return f"Great candidate for MCP server development. Estimated {total_effort} hours for full implementation."
        elif complexity_level == "medium":
            return f"Good MCP server candidate. Consider phased implementation over {total_effort} hours."
        else:
            return f"Complex but valuable MCP server. Recommend starting with quick wins, full implementation ~{total_effort} hours."